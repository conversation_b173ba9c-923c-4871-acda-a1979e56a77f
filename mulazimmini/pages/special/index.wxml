<wxs src="../discount/discountwxs.wxs" module="seck" />
<view class="header_img" style="{{heightHeader}}">
    <view class="header" style="{{getList.length>2?'':langId==1?'direction: rtl;':''}}">
        <scroll-view class="scrollview" scroll-x scroll-with-animation="{{withAnimation}}" scroll-left="{{scrollX}}"
            scroll-into-view="{{toViewString}}">
            <view class="scroll-body" style="{{langId==1?'direction: rtl;':''}}">
                <view class="scroll-title" wx:for="{{getList}}" style="{{index==active?'color:#fff':''}}" wx:key="index"
                    bindtap="onTap" data-id="{{index}}" id="menu-{{index}}">
                    <view class="margin-top" style="{{langId==2?'margin-bottom: 15rpx;':''}}">
                        <text class="price_num">{{item.price}}</text><text
                            style="{{langId==1?'padding: 0 4rpx;':''}}">{{lang.special}}</text>
                    </view>
                    <view class="scroll-active"
                        style="{{langId==1?'width: 120rpx;':'width: 70rpx;'}} {{index==active?'opacity: 1;':'opacity: 0;'}}">
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
</view>
<view class="{{loading?'loading':''}}">
    <navigation my_class="{{true}}" has_home="{{true}}" has_back='{{true}}'></navigation>
</view>
<view class="body" style="top:calc({{getSystem.statusBarHeight + 44}}px + {{getList.length!=0?'110rpx':'0rpx'}});">
    <view class="content"
        style="height: calc(100vh - {{getList.length!=0?'110rpx':'0rpx'}} - {{getSystem.statusBarHeight + 44}}px);">
        <scroll-view scroll-y scroll-with-animation scroll-into-view="{{toScrollView}}" scroll-top="{{scrollTop}}"
            class="content-scroll" bindscroll="onTabsScroll" refresher-threshold="{{refresherHeight}}"
            refresher-enabled="{{true}}" refresher-default-style="none" refresher-triggered="{{triggered}}"
            bindrefresherrefresh="onRefresh">
            <view class="refresh-container" slot="refresher">
                <refreshLoading size="75rpx" />
            </view>
            <view class="content-body {{ langId == 1 ? 'bodyRtl':'bodyLtr' }}" wx:if="{{getList.length!=0}}"
                style="{{langId==1?'direction: rtl;':''}}">
                <view class="market-item" wx:for="{{getList}}" wx:key="index" id="scroll-{{index}}">
                    <view class="market-title">
                        <view class="market-title-price">
                            <view class="market-flexs">
                                <view class="market-title-prices"><text>{{lang.special_price}}</text> <text
                                        class="price-market price_num">{{item.price}}</text><text>{{lang.rmb}}</text>
                                </view>
                                <view class="reguler" bindtap="{{item.rule?'onRegular':''}}" data-text="{{item.rule}}">
                                    <view wx:if="{{item.shipment_type==1}}" class="shipment_type">
                                        <view wx:if="{{item.shipment_fee > 0}}" class="shipment_fee">
                                            <view class="shipment_fee_price">
                                                <text>{{item.shipment_fee}}</text>
                                                <text class="shipment_fee_currency">￥</text>
                                            </view>
                                            <view class="shipment_fee_text">{{lang.distribution}}</view>
                                        </view>
                                        <!-- 免费配送费 -->
                                        <view wx:else>
                                            {{lang.ask_for_shipment}}
                                        </view>
                                    </view>
                                    <image wx:if="{{item.rule}}" src="/resources/img/reguler.svg" />
                                </view>
                            </view>
                            <view class="market-title-time">
                                <image class="notice" src="https://acdn.mulazim.com/wechat_mini/img/resources/notice.png" mode="aspectFit">
                                </image>
                                <view style="direction: ltr;">{{item.is_begin?item.end_time:item.begin_time}}</view>
                                <view>
                                    {{item.is_begin?lang.special_time:lang.special_begin_time}}</view>
                            </view>
                        </view>
                    </view>
                    <view class="res-body" wx:for="{{item.restaurant}}" wx:for-item="res" wx:for-index="index1"
                        wx:key="index1" wx:if="{{index1<item.pageCount}}">
                        <view class="res-items">
                            <view class="res-logo">
                                <image src="{{res.restaurant_logo}}" />
                            </view>
                            <view class="res-content">
                                <view class="res-name">{{res.restaurant_name}}</view>
                                <view class="res-distance">{{res.distance}}</view>
                            </view>
                        </view>
                        <view class="food-item" wx:for="{{res.items}}" wx:for-item="food" wx:for-index="index2"
                            wx:key="index2" data-food="{{food}}" data-resid="{{res.restaurant_id}}"
                            data-distribution="{{res.distribution}}" data-resname="{{res.restaurant_name}}"
                            data-shipment-fee="{{item.shipment_fee}}" data-customer-type="{{item.customer_type}}"
                            data-user-type="{{item.user_type}}" data-reslogo="{{res.restaurant_logo}}"
                            data-marketstate="{{item.state}}" data-isbegin="{{item.is_begin}}"
                            data-marketid="{{item.id}}" data-resstate="{{res.restaurant_state}}" bindtap="details">
                            <view class="food-image">
                                <image src="{{food.image}}" />
                            </view>
                            <view class="food-content">
                                <view class="food-name">{{food.food_name}}</view>
                                <!-- user_type != 2 //不要参加 -->
                                <view class="newUserTagBox" wx:if="{{item.customer_type == 2}}">
                                    <view class="newUserTagtitleBox">
                                        <image class="newUserTagImg" src="/resources/shipment-reduce/fireyellow.png"
                                            mode="aspectFit" lazy-load="false" binderror="" bindload="" />
                                        <div>{{lang.new_user}}</div>
                                    </view>
                                    <div class="newUserTagPricebox">
                                        <div class="newUserTagBoxPrice price_num">
                                            <text>{{food.price}}</text>
                                            <text style="font-size: 30rpx;">¥</text>
                                        </div>
                                        <div class="newUserTagBoxOldPrice">
                                            <text>{{food.old_price}}</text>
                                            <text>¥</text>
                                        </div>
                                    </div>
                                </view>
                                <view class="btn-price-cont" style="{{item.customer_type == 2?'gap:20rpx':''}}">
                                    <view class="progress-wrap" wx:if="{{item.customer_type == 2}}"
                                        style="width: 50%; {{item.is_begin?'':'background: linear-gradient(270.0deg, rgb(203, 202, 202), rgb(234 234 234));'}}">
                                        <view class="progres"
                                            style="transition: 1s; width:{{food.saled_count/food.total_count*100}}%;">
                                        </view>
                                    </view>
                                    <view class="food-price" wx:else>
                                        <image src="/resources/img/huo.png" />
                                        <view class="food-price-ps price_num"><text
                                                style="font-size: 24rpx;">¥</text>{{food.price}}</view>
                                        <view class="food-origin-price">¥{{food.old_price}}</view>
                                    </view>
                                    <view class="btns"
                                        style="{{seck.getButtonStyle(item, food.total_count, food.saled_count,food.state,res.restaurant_state)}}">
                                        {{seck.getButtonText(item, food.total_count,
                                        food.saled_count,lang,food.state,res.restaurant_state)}}
                                    </view>
                                </view>
                                <!-- 百分比 -->
                                <view class="progress-wrap" wx:if="{{item.customer_type != 2}}"
                                    style="{{item.is_begin?'':'background: linear-gradient(270.0deg, rgb(203, 202, 202), rgb(234 234 234));'}}">
                                    <view class="progres"
                                        style="transition: 1s; width:{{food.saled_count/food.total_count*100}}%;">
                                    </view>
                                </view>
                                <view class="take_time" wx:if="{{food.take_time}}">
                                    <text>{{lang.distribution_time}}</text>
                                    <text style="padding: 0 6rpx;">{{food.take_time}}</text>
                                    <text>{{lang.seckill_minute}}</text>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="showMore" style="{{item.pageCount==10?'display: none;':''}}" bind:tap="showMore"
                        data-id="{{index}}" data-page="{{item.pageCount}}" wx:if="{{item.restaurant.length>2}}">
                        <view>{{item.pageCount==2?lang.all:lang.special_more}}</view>
                        <view style="{{item.pageCount == 999?'transform: rotate(180deg);':''}}"
                            class="iconfont icon-icon-arrow-top2-copy-copy"></view>
                    </view>
                </view>
            </view>
            <view wx:if="{{getList.length==0}}" class="noitem">
                <image class="no-data-img" src="https://acdn.mulazim.com/wechat_mini/img/resources/searchcong.png"
                    mode="aspectFit" />
                <view class="no-data">{{lang.selfpickupordercontainer}}</view>
            </view>
        </scroll-view>
    </view>
</view>

<view class="food-details" wx:if="{{detailView}}">
    <view class="transparent {{transparentOpen?'transparent-open':''}}" bind:tap="close"></view>
    <view class="close-img {{transparentOpen?'transparent-open':''}}" bind:tap="close"
        style="top: calc({{( getSystem.statusBarHeight + 44)}}px + 90rpx);">
        <image src="/resources/img/spcialClose.svg" />
    </view>
    <view class="details-body {{detailView?'details-open':''}}"
        style="height: calc({{(getSystem.windowHeight - getSystem.statusBarHeight - 44)}}px - 180rpx);">
        <scroll-view scroll-y class="det-scroll">
            <view class="det-content" style="{{langId==1?'direction: rtl;':''}}">
                <view class="det-img">
                    <image src="{{detail.image}}" mode="widthFix" />
                </view>

                <view class="title-price">
                    <view class="det-title">{{detail.food_name}}</view>
                    <view class="det-price"><text style="font-size: 24rpx;">¥</text><text>{{detail.price}}</text></view>
                </view>

                <view class="add-title">
                    <view>{{lang.special_order_num}}</view>
                    <view class="det-add-order">
                        <view class="iconfont icon-jian1" bind:tap="minusOrder" data-food="{{detail.id}}"></view>
                        <view class="order-input">{{detail.count}}</view>
                        <view class="iconfont icon-jia1" bind:tap="addOrder" data-food="{{detail.id}}"></view>
                    </view>
                </view>
                <view class="spec-container-type-one" wx:if="{{detail.food_type==1 && detail.spec_options_name}}">
                    <rich-text nodes="{{detail.spec_options_name}}" />
                </view>
                <view class="det-order-content">
                    <text>{{lang.special_content1}}</text>
                    <text class="det-order-content-price">{{detail.max_order_count}}</text>
                    <text>{{lang.special_content2}}</text>
                </view>
                <view class="combo-food-box" wx:if="{{detail.food_type == 2 && detail.combo_food_items.length}}">
                    <view class="combo-food-box-list">
                        <view class="combo-food-box-item-wrap" wx:for="{{detail.combo_food_items}}" wx:key="index">
                            <view class="combo-food-box-item">
                                <view class="combo-food-box-item-img">
                                    <image src="{{item.img}}" />
                                </view>
                                <view class="combo-food-box-content">
                                    <view class="combo-food-box-content-name-and-price">
                                        <view class="combo-food-box-content-name">{{item.name}}</view>
                                        <view class="combo-food-box-content-price">
                                            <text class="combo-food-box-content-price-new">¥{{item.price}}</text>
                                        </view>
                                    </view>
                                    <view class="combo-food-box-content-count">
                                        <text class="combo-food-box-content-count-text">x{{item.count}}</text>
                                    </view>
                                </view>
                            </view>
                            <view class="spec-container" wx:if="{{item.spec_options_name}}">
                                <rich-text nodes="{{item.spec_options_name}}"></rich-text>
                            </view>
                        </view>

                    </view>
                </view>

            </view>
            <view class="score-body" style="{{langId==1?'direction: rtl;':''}}">
                <view class="score-title">
                    <view class="border-right"></view>
                    <view style="padding: 0 10rpx;">{{lang.special_comment}}</view>
                </view>

                <view class="score-tap-list">
                    <view class="score-tap-list-btn"
                        style="{{ item.type == navId ? 'background: linear-gradient(270.00deg, var(--theme-main-color), var(--theme-main-color-dark) 100%);color:#fff':''}}"
                        wx:for="{{typeList}}" data-id="{{item.type}}" data-food-id="{{detail.food_id}}"
                        data-res-id="{{merchant_id}}" bind:tap="scoreNav" wx:key="index">{{item.name}} ({{item.count}})
                    </view>
                </view>
                <view class="commentList" wx:if="{{commentList.length!=0}}">
                    <comment wx:for="{{commentList}}" wx:key="index" item="{{item}}"></comment>
                </view>
                <view class="selfpickupordercontainer" wx:if="{{commentList.length==0}}">
                    <image class="no-data-img" src="https://acdn.mulazim.com/wechat_mini/img/resources/searchcong.png"
                        mode="aspectFit" />
                    <view class="no-data">{{lang.comment_no_data}}</view>
                </view>
            </view>
            <view class="empty-bottom"></view>
        </scroll-view>
        <view class="card-view"></view>
        <view class="card">
            <view class="border-line">
                <view class="card-border">
                    <image src="/resources/img/card_num.png" />
                    <view class="card-num">{{cartNum.count}}</view>
                </view>
                <view class="card-item">
                    <view class="card-price">
                        <view class="card-total-price">
                            <view class="card_price"><text
                                    style="font-size: 30rpx;">¥</text><text>{{cartNum.price}}</text></view>
                            <view class="card_old_price" wx:if="{{cartNum.old_price}}"><text
                                    style="font-size: 30rpx;">¥</text><text>{{cartNum.old_price}}</text></view>
                        </view>
                        <view class="lunch_box_distribution" style="{{langId==2?'font-size: 28rpx;':''}}">
                            <view class="distribution" style="{{langId==1?'':'flex-flow: row-reverse;'}}">
                                <text style="{{langId==1?'':'display: flex;align-items: center;'}}">
                                    <text style="font-size: 22rpx;">¥</text>
                                    <text>{{shipment}}</text>
                                </text>
                                <text style="padding: 0 2rpx;">:</text>
                                <text>{{lang.distribution}}</text>
                            </view>
                            <view class="lunch_box" wx:if="{{lunch_box_price}}"
                                style="{{langId==1?'':'flex-flow: row-reverse;'}}">
                                <text style="{{langId==1?'':'display: flex;align-items: center;'}}">
                                    <text style="font-size: 22rpx;">¥</text>
                                    <text>{{lunch_box_price}}</text>
                                </text>
                                <text style="padding: 0 2rpx;">:</text>
                                <text>{{lang.lunch_box}}</text>
                            </view>
                        </view>
                    </view>
                    <view class="submit-form" bindtap="{{cartNum.price==0?'':'submitOrder'}}"
                        style="{{cartNum.price==0?'background: #333333;':'background: linear-gradient(270.00deg, var(--theme-main-color), var(--theme-main-color-dark) 100%);'}}">
                        {{lang.complete}}
                    </view>
                </view>
            </view>
        </view>
    </view>
</view>


<view class="food-details" wx:if="{{regularView}}">
    <view class="transparent {{regularOpen?'transparent-open':''}}" bindtap="regularClose"></view>
    <view class="close-img {{regularOpen?'transparent-open':''}}" bindtap="regularClose"
        style="top: calc({{( getSystem.statusBarHeight + 44)}}px + 370rpx);">
        <image src="/resources/img/spcialClose.svg" />
    </view>
    <view class="details-body {{regularView?'details-open':''}}"
        style="height: calc({{(getSystem.windowHeight - getSystem.statusBarHeight - 44)}}px - 450rpx);">
        <view class="reguler-title">{{lang.activity_rules}}</view>
        <scroll-view scroll-y class="regularText">
            <view class="reguler-text" style="{{langId==1?'direction: rtl;':''}}">
                {{regularText}}
            </view>
        </scroll-view>
    </view>
</view>

<loading id="loading"></loading>
<toast id="toast"></toast>
<!-- 优惠券弹出框 -->
<courtesyCard id="courtesyTip" wx:if="{{isCourtesyModal}}" class="{{isCourtesyModal?'discount-show':'discount-close'}}"
    couponNotice="{{ couponNotice }}" couponData="{{couponData}}" bind:show="showCouponModal"
    bind:cancel="closeCouponModal" bind:confirm="confirmCoupon" />
<!-- 老用户显示弹出框 -->
<businessTimeModal wx:if="{{showBusinessTimeDialog}}" title="{{lang.not_new_user_title}}"
    content="{{lang.not_new_user_tip}}" bind:close="closeBusinessTimeHandler" lang="{{lang}}" langId="{{langId}}"
    lineHight="60" />