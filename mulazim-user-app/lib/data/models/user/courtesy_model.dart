/// 优惠券列表响应
class CourtesyListResponse {
  /// 构造函数
  const CourtesyListResponse({
    required this.list,
    required this.notNow,
    required this.used,
    required this.expired,
    required this.total,
    required this.page,
    required this.limit,
    this.newCoupons = const [],
  });

  /// 从JSON构造
  factory CourtesyListResponse.fromJson(final Map<String, dynamic> json) {
    return CourtesyListResponse(
      list: (json['list'] as List<dynamic>?)
              ?.map((e) => Courtesy.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      notNow: (json['not_now'] as List<dynamic>?)
              ?.map((e) => Courtesy.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      used: (json['used'] as List<dynamic>?)
              ?.map((e) => Courtesy.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      expired: (json['expired'] as List<dynamic>?)
              ?.map((e) => Courtesy.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      newCoupons: (json['new'] as List<dynamic>?)
              ?.map((e) => Courtesy.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      total: json['total'] as int? ?? 0,
      page: json['page'] as int? ?? 1,
      limit: json['limit'] as int? ?? 10,
    );
  }

  /// 可使用的优惠券列表
  final List<Courtesy> list;

  /// 未到使用时间的优惠券列表
  final List<Courtesy> notNow;

  /// 已使用的优惠券列表
  final List<Courtesy> used;

  /// 已过期的优惠券列表
  final List<Courtesy> expired;

  /// 新的优惠券列表
  final List<Courtesy> newCoupons;

  /// 总数
  final int total;

  /// 当前页码
  final int page;

  /// 每页数量
  final int limit;

  /// 是否有更多数据
  bool get hasMore => total > (page * limit);
}

/// 优惠券
class Courtesy {
  /// 构造函数
  const Courtesy({
    required this.id,
    required this.userId,
    required this.couponId,
    required this.state,
    required this.cityId,
    required this.areaId,
    required this.startTime,
    required this.endTime,
    required this.startUseTime,
    required this.endUseTime,
    required this.lotteryOrderId,
    required this.couponType,
    required this.userCouponStartUseTime,
    required this.userCouponEndUseTime,
    required this.price,
    required this.minPrice,
    required this.count,
    required this.notice,
    required this.areaName,
    required this.useArea,
    this.area,
  });

  /// 从JSON构造
  factory Courtesy.fromJson(final Map<String, dynamic> json) {
    return Courtesy(
      id: json['id'] != null ? json['id'] as int : 0,
      userId: json['user_id'] != null ? json['user_id'] as int : 0,
      couponId: json['coupon_id'] != null ? json['coupon_id'] as int : 0,
      state: json['state'] != null ? json['state'] as int : 0,
      cityId: json['city_id'] != null ? json['city_id'] as int : 0,
      areaId: json['area_id'] != null ? json['area_id'] as int : 0,
      startTime: json['start_time'] != null ? json['start_time'] as String : '',
      endTime: json['end_time'] != null ? json['end_time'] as String : '',
      startUseTime: json['start_use_time'] != null
          ? json['start_use_time'] as String
          : '',
      endUseTime:
          json['end_use_time'] != null ? json['end_use_time'] as String : '',
      lotteryOrderId: json['lottery_order_id'] != null
          ? json['lottery_order_id'] as int
          : 0,
      couponType: json['coupon_type'] != null ? json['coupon_type'] as int : 0,
      userCouponStartUseTime: json['user_coupon_start_use_time'] != null
          ? json['user_coupon_start_use_time'] as String
          : '',
      userCouponEndUseTime: json['user_coupon_end_use_time'] != null
          ? json['user_coupon_end_use_time'] as String
          : '',
      price: json['price'] != null ? num.parse(json['price'].toString()) : 0,
      minPrice: json['min_price'] != null
          ? num.parse(json['min_price'].toString())
          : 0,
      count: json['count'] != null ? json['count'] as int : 0,
      notice: json['notice'] != null ? json['notice'] as String : '',
      areaName: json['area_name'] != null ? json['area_name'] as String : '',
      useArea: json['use_area'] != null ? json['use_area'] as String : '',
      area: json['area'] != null ? json['area'] as dynamic : json['area'],
    );
  }

  /// ID
  final int id;

  /// 用户ID
  final int userId;

  /// 优惠券ID
  final int couponId;

  /// 状态
  final int state;

  /// 城市ID
  final int cityId;

  /// 区域ID
  final int areaId;

  /// 开始时间
  final String startTime;

  /// 结束时间
  final String endTime;

  /// 开始使用时间
  final String startUseTime;

  /// 结束使用时间
  final String endUseTime;

  /// 抽奖订单ID
  final int lotteryOrderId;

  /// 优惠券类型
  final int couponType;

  /// 用户优惠券开始使用时间
  final String userCouponStartUseTime;

  /// 用户优惠券结束使用时间
  final String userCouponEndUseTime;

  /// 金额
  final num price;

  /// 最低消费金额
  final num minPrice;

  /// 数量
  final int count;

  /// 说明
  final String notice;

  /// 区域名称
  final String areaName;

  /// 使用区域
  final String useArea;

  /// 区域
  final dynamic area;
}
