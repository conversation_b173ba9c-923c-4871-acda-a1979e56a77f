import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/providers/core_providers.dart';
import 'package:user_app/core/storage/storage_service.dart';
import 'package:user_app/core/utils/native_call.dart';
import 'package:user_app/core/utils/native_communicator.dart';
import 'package:user_app/core/widgets/custom_refresh_indicator.dart';
import 'package:user_app/core/widgets/empty_view.dart';
import 'package:user_app/core/widgets/skeleton_widget.dart';
import 'package:user_app/features/activity/pages/user_ranking/widgets/ranking_card_widget.dart';
import 'package:user_app/features/home/<USER>/index/home_controller.dart';
import 'package:user_app/features/home/<USER>/parts/restaurant_item_part.dart';
import 'package:user_app/features/home/<USER>/widgets/header_widget.dart';
import 'package:user_app/features/home/<USER>/widgets/home_skeleton.dart';
import 'package:user_app/features/home/<USER>/widgets/location_bar_widget.dart';
import 'package:user_app/features/home/<USER>/widgets/sliver_headers_widget.dart';
import 'package:user_app/features/home/<USER>/widgets/open_location_widget.dart';
import 'package:user_app/core/theme/app_colors.dart';
import 'package:user_app/features/restaurant/pages/restaurant_detail/widgets/dialogs/animate_confirm_send_dialog.dart';
import 'package:user_app/generated/l10n.dart';
import 'package:user_app/routes/paths.dart';
import 'package:user_app/main.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage>
    with WidgetsBindingObserver {
  late final ScrollController _scrollController;
  bool _isScrolling = false; // 添加滚动状态标记
  Timer? _scrollTimer; // 添加定时器来处理滚动停止检测
  bool _isManuallyHidden = false; // 手动隐藏状态
  DateTime _backgroundTime = DateTime.now(); // 后台停留时间

  @override
  void initState() {
    super.initState();
    // 注册App生命周期监听
    WidgetsBinding.instance.addObserver(this);

    // 初始化 ScrollController
    _scrollController = ScrollController();

    // 添加滚动监听
    _scrollController.addListener(_onScrollChanged);

    // 使用一次性的 PostFrameCallback 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((final _) async {
      // 将 ScrollController 注册到 provider
      ref.read(homeScrollControllerProvider.notifier).state = _scrollController;

      // 初始化首页数据
      ref.read(homeControllerProvider.notifier).initHomePage(context);

      await getRegIdAndWriteStorage();

      NativeCommunicator.setupChannel(context);

      await Future.delayed(Duration(seconds: 3));

      if (ref.read(isLoggedInProvider)) {
        await _openNotify();
      }
    });
  }

  /// App生命周期变化回调
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // App从后台回到前台，刷新首页数据
      if (ref.read(isOpenPermissionSettingProvider)) {
        ref
            .read(homeControllerProvider.notifier)
            .initHomePage(context, isRefresh: true);
      }
      // 刷新首页数据,根据后台停留时间刷新，后台停留时间大于5分钟刷新
      final backgroundTime = DateTime.now().difference(_backgroundTime);
      if (backgroundTime.inMinutes >= 10) {
        // 滚动到顶部
        _scrollController.jumpTo(0);
        ref.read(homeControllerProvider.notifier).refreshHomeData(
              context: context,
            );
        _backgroundTime = DateTime.now();
      }
    }
    if (state == AppLifecycleState.paused) {
      // App进入后台，记录后台停留时间
      _backgroundTime = DateTime.now();
    }
    // 你可以根据需要处理其他生命周期状态
    // AppLifecycleState.inactive, AppLifecycleState.paused, AppLifecycleState.detached
  }

  /// 滚动监听回调
  void _onScrollChanged() {
    // 取消之前的定时器
    _scrollTimer?.cancel();

    // 设置滚动状态为true
    if (!_isScrolling) {
      setState(() {
        _isScrolling = true;
      });
    }

    // 设置定时器，500ms后如果没有新的滚动事件，则认为停止滚动
    _scrollTimer = Timer(Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isScrolling = false;
        });
      }
    });
  }

  ///是否打开了通知开关
  Future<bool> isOpenNotify() async {
    if (Platform.isAndroid) {
      String value = await NativeCall().isOpenNotify();
      if (value == 'no') {
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  ///打开通知开关
  Future<void> _openNotify() async {
    bool isOpen = await isOpenNotify();
    if (!isOpen) {
      showGeneralDialog(
        transitionDuration: const Duration(milliseconds: 100),
        transitionBuilder: (BuildContext context, Animation<double> animation,
            Animation<double> secondaryAnimation, Widget child) {
          return ScaleTransition(scale: animation, child: child);
        },
        context: context,
        pageBuilder: (BuildContext context, Animation<double> animation,
            Animation<double> secondaryAnimation) {
          return ConfirmSendDialog(
            content: S.current.open_notify_remark,
            screenWidth: MediaQuery.of(context).size.width,
          );
        },
      ).then((value) async {
        if (value != null) {
          String state = value.toString() ?? '';
          if (state == 'yes') {
            await openSettingsForNotification();
          }
        }
      });
    }
  }

  ///跳转通知设置页面
  Future<void> openSettingsForNotification() async {
    if (Platform.isAndroid) {
      await NativeCall().openSettingsForNotification();
    } else {
      // await openAppSettings();
    }
  }

  ///获取jpush的注册id
  Future<void> getRegIdAndWriteStorage() async {
    // if (Platform.isAndroid) {
    String regId = await NativeCall().getRegId();
    await StorageService().write('regId', regId);
    // }
  }

  @override
  void dispose() {
    // 清理定时器
    _scrollTimer?.cancel();
    // 注销App生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    // 清除 provider 中的 ScrollController 引用
    // 在dispose中避免使用ref，因为widget已经被销毁
    // ref.read(homeScrollControllerProvider.notifier).state = null;
    // 释放 ScrollController
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 路由监听注册（可选，需在MaterialApp中配置RouteObserver）
    // routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  Widget build(final BuildContext context) {
    // 使用 ConsumerBuilder 可以细粒度控制重建范围
    return Scaffold(
      body: Stack(
        children: [
          // 主要内容
          Column(
            children: [
              Stack(
                children: [
                  // 背景容器放在最底层
                  Container(
                    decoration: BoxDecoration(
                      color: AppColors.baseGreenColor,
                      image: DecorationImage(
                        image: AssetImage(
                          'assets/images/header_img.png',
                        ), // 本地图片
                        fit: BoxFit.fitHeight, // 让图片覆盖整个容器
                      ),
                    ),
                    child: Column(
                      children: [
                        // 头部搜索始终显示
                        const HeaderWidget(),

                        // 位置信息栏始终显示
                        const LocationBarWidget(),
                      ],
                    ),
                  ),
                ],
              ),

              // 主体内容部分 - 使用 Consumer 限制刷新范围
              Expanded(
                child: Consumer(
                  builder: (final context, final ref, final child) {
                    // 获取所需状态，使用 select 减少不必要的重建
                    final homeData = ref.watch(homeControllerProvider
                        .select((final state) => state.homeData));
                    final isInitialLoading = ref.watch(homeControllerProvider
                        .select((final state) => state.isInitialLoading));
                    final error = ref.watch(homeControllerProvider
                        .select((final state) => state.error));
                    final isPermissionError = ref.watch(homeControllerProvider
                        .select((final state) => state.isPermissionError));
                    final isNoServiceAgent = ref.watch(homeControllerProvider
                        .select((final state) => state.isNoServiceAgent));

                    if (isPermissionError) {
                      return Container(
                        color: AppColors.baseBackgroundColor,
                        child: OpenLocationWidget(
                          isPermissionError: isPermissionError,
                        ),
                      );
                    }

                    // 显示OpenLocation错误页面（当有errorType时显示）
                    if (isNoServiceAgent) {
                      return Container(
                        color: AppColors.baseBackgroundColor,
                        child: OpenLocationWidget(),
                      );
                    }

                    // // 显示错误页面（传统的error显示）
                    if (error != null) {
                      return EmptyView(
                        message: error,
                        onRetry: () {
                          ref
                              .read(homeControllerProvider.notifier)
                              .initHomePage(context);
                        },
                      );
                    }

                    // 获取头部组件
                    final headerWidget = SliverHeadersWidget(data: homeData);
                    final headerSlivers = headerWidget.build(context, ref);

                    return Container(
                      color: AppColors.baseBackgroundColor,
                      // 在这里使用Skeleton.overlay来控制内容区域的骨架屏显示
                      // 只在初始加载时显示骨架屏，下拉刷新时不显示
                      child: Skeleton.overlay(
                        isLoading: isInitialLoading,
                        skeleton: const HomeSkeleton(),
                        child: _buildRefreshableContent(headerSlivers),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
          // 排行榜活动入口卡片
          _buildRankingCard(),
        ],
      ),
    );
  }

  /// 构建排行榜活动入口卡片
  Widget _buildRankingCard() {
    final homeData = ref
        .watch(homeControllerProvider.select((final state) => state.homeData));
    final isInitialLoading = ref.watch(
        homeControllerProvider.select((final state) => state.isInitialLoading));

    // 安全地检查ranking数组
    final rankingList = homeData?.ranking;
    bool isShowRankingCard = rankingList != null && rankingList.isNotEmpty;

    // 如果正在初始加载且没有数据，隐藏卡片
    if (isInitialLoading && homeData == null) {
      return const SizedBox.shrink();
    }

    // 如果数组为空或不存在，返回空容器
    if (!isShowRankingCard) {
      return const SizedBox.shrink();
    }
    final isUg = ref.watch(languageProvider) == 'ug';
    final padding = _isScrolling || _isManuallyHidden ? -90.w : 12.w;
    return AnimatedPositioned(
      duration: Duration(milliseconds: 400),
      curve: Curves.easeInOut,
      bottom: MediaQuery.of(context).size.height * 0.2,
      right: isUg ? null : padding,
      left: isUg ? padding : null, // 滚动时或手动隐藏时隐藏到左边，留更多显示部分
      child: RankingCardWidget(
        rankingList: rankingList,
        onTap: (extraData) {
          context.push(
            AppPaths.userRankingPage,
            extra: extraData,
          );
        },
        onClose: () {
          setState(() {
            _isManuallyHidden = true;
          });
        },
        onShow: () {
          setState(() {
            _isManuallyHidden = false;
          });
        },
        isHidden: _isManuallyHidden,
      ),
    );
  }

  // 提取可刷新内容构建逻辑
  Widget _buildRefreshableContent(final List<Widget> headerSlivers) {
    return Consumer(
      builder: (final context, final ref, final child) {
        final controller = ref.read(homeControllerProvider.notifier);
        final state = ref.watch(homeControllerProvider);

        // 刷新方法
        Future<void> onRefresh() async {
          await controller.refreshHomeData(context: context);
        }

        // 加载更多方法
        Future<void> onLoading() async {
          if (state.canAddRestaurantData && !state.isLoadingMore) {
            await controller.handleScrollToBottom();
          }
        }

        return CustomRefreshIndicator(
          onRefresh: onRefresh,
          color: AppColors.primary,
          enablePullUp: true,
          onLoading: onLoading,
          hasMoreData: state.canAddRestaurantData,
          hideNoMore: true,
          child: CustomScrollView(
            // 使用我们的 ScrollController
            controller: _scrollController,
            // 添加物理滚动效果
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // 添加头部组件
              ...headerSlivers,

              // 使用SliverList代替SliverToBoxAdapter，使餐厅列表可以和头部联动滑动
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (final context, final index) {
                    return RestaurantItemPart(
                      key: ValueKey(
                          'restaurant_${state.restaurantList[index].id}'),
                      restaurantItem: state.restaurantList[index],
                    );
                  },
                  childCount: state.restaurantList.length,
                  // 添加性能优化选项
                  addAutomaticKeepAlives: true,
                  addRepaintBoundaries: true,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
