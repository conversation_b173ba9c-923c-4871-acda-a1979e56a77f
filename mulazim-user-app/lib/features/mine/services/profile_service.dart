import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:user_app/core/utils/permission_helper.dart';
import 'package:user_app/data/models/auth/auth_model.dart';
import 'package:user_app/data/models/user/user_request_model.dart';
import 'package:user_app/data/repositories/user/user_repository.dart';
import 'package:user_app/features/mine/pages/mine/mine_controller_provider.dart';
import 'package:user_app/features/mine/services/profile_ui_service.dart';
import 'package:user_app/generated/l10n.dart';

part 'profile_service.g.dart';

/// UI服务提供者
@riverpod
ProfileUiService uiService(final Ref ref) => ProfileUiService();

/// 个人资料业务逻辑
@riverpod
class ProfileService extends _$ProfileService {
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void build() {}

  /// 加载用户数据
  Future<UserInfo?> loadUserData() async {
    try {
      final userRepository = ref.read(userRepositoryProvider);
      final userInfo = userRepository.getUserInfo();
      return userInfo;
    } catch (e) {
      BotToast.showText(text: '加载用户数据失败');
      rethrow;
    }
  }

  /// 更新用户信息
  Future<void> updateUserInfo(final UserInfo userInfo) async {
    try {
      final userRepository = ref.read(userRepositoryProvider);
      await userRepository.updateUserInfo(userInfo);
      ref.read(mineControllerProvider.notifier).refreshUserData();
    } catch (e) {
      rethrow;
    }
  }

  /// 选择并上传头像
  Future<String?> pickAndUploadAvatar() async {
    final source = await ref.read(uiServiceProvider).showImageSourceDialog();
    if (source == null) return null;
    Permission permission = Permission.photos;
    String explanation = S.current.photos_permission_explanation;
    if (source == ImageSource.camera) {
      permission = Permission.camera;
      explanation = S.current.camera_permission_explanation;
    } else if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      permission = androidInfo.version.sdkInt <= 32
          ? Permission.storage
          : Permission.photos;
      explanation = S.current.photos_permission_explanation;
    }
    // 在显示选择器之前，请求相机和相册权限
    final permissionResults = await PermissionHelper.requestPermission(
      permission: permission,
      explanation: explanation,
      title: S.current.profile_photo_permissions,
    );

    if (!permissionResults) {
      BotToast.showText(
        text: S.current.permissions_required
            .replaceAll('%s', S.current.profile_photo_permissions),
      );
      return null;
    }
    return _getImage(source);
  }

  /// 获取图片
  Future<String?> _getImage(final ImageSource source) async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        // 如果是网络图片，直接返回
        if (pickedFile.path.startsWith('http')) {
          return pickedFile.path;
        }

        // 上传图片到服务器
        final userRepository = ref.read(userRepositoryProvider);
        final uploadResult = await userRepository.uploadImage(
          filePath: pickedFile.path,
          typeName: 'user_avatar',
        );

        if (uploadResult.success) {
          BotToast.showText(text: '头像更新成功');
          return uploadResult.data?['url'];
        } else {
          BotToast.showText(text: uploadResult.msg);
          return null;
        }
      }
    } catch (e) {
      BotToast.showText(text: '选择图片失败：$e');
    }
    return null;
  }

  /// 绑定微信用户信息
  Future<void> bindWechatInfo({
    required final String name,
    final String? avatar,
    final int? gender,
    final String? birthDay,
  }) async {
    try {
      final user = UserRequestModel(
        name: name,
        avatar: avatar,
        gender: gender,
        birthDay: DateFormat('yyyy-MM-dd')
            .format(DateFormat('yyyy/MM/dd').parse(birthDay ?? '')),
      );

      final result =
          await ref.read(userRepositoryProvider).bindWechatInfo(user);
      if (!result.success) {
        throw Exception(result.msg);
      }
      BotToast.showText(text: result.msg);
    } catch (e) {
      BotToast.showText(text: '$e');
      rethrow;
    }
  }
}
